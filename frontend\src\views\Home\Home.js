import React from 'react';
import { useNavigate } from 'react-router-dom';
import UrlInputForm from '../../components/analysis/UrlInputForm';

const Home = () => {
  const navigate = useNavigate();

  const handleAnalysisSubmit = async (analysisId) => {
    // Navigate to the analysis page to show results
    navigate(`/analysis/${analysisId}`);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4">
      {/* Hero Section */}
      <div className="text-center max-w-4xl mx-auto">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Web Accessibility Analyzer
        </h1>
        <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          Comprehensive web accessibility analysis and reporting.
          Ensure your website meets WCAG guidelines.
        </p>

        {/* URL Input Form */}
        <div className="w-full max-w-2xl mx-auto">
          <UrlInputForm onSubmit={handleAnalysisSubmit} />
        </div>
      </div>
    </div>
  );
};

export default Home;
