import React from 'react';
import { useNavigate } from 'react-router-dom';
import UrlInputForm from '../../components/analysis/UrlInputForm';

const Home = () => {
  const navigate = useNavigate();

  const handleAnalysisSubmit = async (analysisId) => {
    // Navigate to the analysis page to show results
    navigate(`/analysis/${analysisId}`);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center px-4">
      {/* Hero Section */}
      <div className="text-center max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight mb-6">
          Web Accessibility Analyzer
        </h1>
        <p className="text-lg md:text-xl text-gray-600 mb-12 max-w-2xl mx-auto leading-relaxed">
          Comprehensive web accessibility analysis and reporting.
          Ensure your website meets WCAG guidelines
        </p>

        {/* URL Input Form */}
        <div className="w-full max-w-2xl mx-auto">
          <UrlInputForm onSubmit={handleAnalysisSubmit} />
        </div>
      </div>
    </div>
  );
};

export default Home;
