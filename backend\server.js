const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// Initialize Firebase Admin SDK
const { initializeFirebase } = require('./config/firebase-admin');

const app = express();
const PORT = process.env.PORT || 5000;

// Initialize Firebase on startup
try {
  initializeFirebase();
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
  process.exit(1);
}

// Middleware
app.use(helmet()); // Security headers

// Configure CORS to allow all origins for now (can be restricted later)
app.use(cors({
  origin: true, // Allow all origins
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(morgan('combined')); // Logging
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Accessibility Analyzer API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    memory: process.memoryUsage()
  });
});

// Simple test API route
app.get('/api', (req, res) => {
  res.json({
    message: 'Accessibility Analyzer API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      analysis: '/api/analysis'
    },
    status: 'working'
  });
});

// Simple analysis endpoint for testing
app.post('/api/analysis', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        error: 'URL is required'
      });
    }

    console.log('Analysis request received for URL:', url);

    // Return a mock successful response for now
    const mockResult = {
      url: url,
      timestamp: new Date().toISOString(),
      status: 'completed',
      summary: {
        totalViolations: 3,
        criticalViolations: 1,
        moderateViolations: 2,
        minorViolations: 0,
        wcagLevel: 'AA',
        score: 85
      },
      violations: [
        {
          id: 'color-contrast',
          impact: 'critical',
          description: 'Elements must have sufficient color contrast',
          help: 'Ensure all text elements have sufficient color contrast between text and background',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.10/color-contrast',
          nodes: [
            {
              target: ['button.primary'],
              html: '<button class="primary">Submit</button>',
              failureSummary: 'Fix any of the following: Element has insufficient color contrast of 2.1 (foreground color: #ffffff, background color: #4a90e2, font size: 14.0pt, font weight: normal). Expected contrast ratio of 4.5:1'
            }
          ]
        },
        {
          id: 'image-alt',
          impact: 'moderate',
          description: 'Images must have alternate text',
          help: 'Ensure images have alternate text or a role of none or presentation',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.10/image-alt',
          nodes: [
            {
              target: ['img[src="logo.png"]'],
              html: '<img src="logo.png">',
              failureSummary: 'Fix any of the following: Element does not have an alt attribute'
            }
          ]
        },
        {
          id: 'heading-order',
          impact: 'moderate',
          description: 'Heading levels should only increase by one',
          help: 'Ensure headings are in a logical order',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.10/heading-order',
          nodes: [
            {
              target: ['h3'],
              html: '<h3>Section Title</h3>',
              failureSummary: 'Fix any of the following: Heading order invalid'
            }
          ]
        }
      ],
      metadata: {
        pageTitle: 'Sample Website',
        pageUrl: url,
        scanDuration: 2500,
        axeVersion: '4.10.3',
        userAgent: 'Accessibility Analyzer Bot'
      }
    };

    res.json({
      message: 'Analysis completed successfully',
      data: mockResult
    });

  } catch (error) {
    console.error('Analysis error:', error);
    res.status(500).json({
      error: 'Analysis failed',
      message: error.message
    });
  }
});

// API routes (commented out for now)
// app.use('/api', require('./routes/index'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'production' ? {} : err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 API URL: http://localhost:${PORT}`);
});

module.exports = app;
