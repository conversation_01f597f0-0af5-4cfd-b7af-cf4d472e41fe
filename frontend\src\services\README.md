# Services Directory

This directory contains API communication services and external integrations.

## Structure

- `api/` - API service modules for backend communication
- `firebase/` - Firebase authentication and configuration
- `utils/` - Service utility functions

## Service Guidelines

- Use axios for HTTP requests
- Implement proper error handling
- Include request/response interceptors
- Use environment variables for API endpoints
- Implement retry logic for failed requests
