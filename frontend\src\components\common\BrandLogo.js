import React from 'react';

const BrandLogo = ({ className = "", size = "default" }) => {
  const sizeClasses = {
    small: "h-8 w-8",
    default: "h-12 w-12",
    large: "h-16 w-16"
  };

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Gradient Heart Logo */}
      <div className={`${sizeClasses[size]} relative`}>
        <svg
          viewBox="0 0 24 24"
          fill="none"
          className="w-full h-full"
        >
          <defs>
            <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#FF6B6B" />
              <stop offset="25%" stopColor="#FF8E53" />
              <stop offset="50%" stopColor="#FF6B9D" />
              <stop offset="75%" stopColor="#C44569" />
              <stop offset="100%" stopColor="#F8B500" />
            </linearGradient>
          </defs>
          <path
            d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            fill="url(#heartGradient)"
            className="drop-shadow-sm"
          />
        </svg>
      </div>
      
      {/* Brand Text */}
      <div className="flex flex-col">
        <span className="text-white font-bold text-lg leading-tight">
          Accessibility
        </span>
        <span className="text-gray-300 font-medium text-sm leading-tight">
          Analyzer
        </span>
      </div>
    </div>
  );
};

export default BrandLogo;
