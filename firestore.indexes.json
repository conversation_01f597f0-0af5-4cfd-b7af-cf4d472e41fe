{"indexes": [{"collectionGroup": "analysisRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "requestTimestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analysisRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "url", "order": "ASCENDING"}, {"fieldPath": "requestTimestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analysisRequests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "completedTimestamp", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "url", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "summary.complianceScore", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "summary.totalIssues", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "url", "order": "ASCENDING"}, {"fieldPath": "summary.complianceScore", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analysisResults", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "summary.complianceScore", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "analysisResults", "fieldPath": "axeCoreResults.violations", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "analysisResults", "fieldPath": "recommendations", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}