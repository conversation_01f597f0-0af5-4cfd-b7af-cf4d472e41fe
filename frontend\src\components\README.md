# Components Directory

This directory contains reusable UI components for the Accessibility Analyzer application.

## Structure

- `common/` - Common UI components (buttons, inputs, modals, etc.)
- `layout/` - Layout components (header, footer, sidebar, etc.)
- `analysis/` - Analysis-specific components
- `dashboard/` - Dashboard-specific components
- `auth/` - Authentication-related components

## Component Guidelines

- Each component should be in its own directory with an index.js file
- Include PropTypes for type checking
- Use Tailwind CSS for styling
- Follow accessibility best practices
- Include JSDoc comments for documentation
