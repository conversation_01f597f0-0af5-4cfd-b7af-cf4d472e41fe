@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Custom Properties for Design System */
:root {
  /* Lovable-inspired color palette */
  --color-lovable-dark: #0f0f23;
  --color-lovable-blue: #7c73ff;
  --color-lovable-purple: #a855f7;
  --color-lovable-pink: #ec4899;

  /* Gradient definitions */
  --gradient-lovable: linear-gradient(135deg, #0f0f23 0%, #1e1b4b 25%, #312e81 50%, #581c87 75%, #831843 100%);
  --gradient-lovable-light: linear-gradient(135deg, #1e1b4b 0%, #312e81 25%, #581c87 50%, #7c3aed 75%, #a855f7 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* Shadow definitions */
  --shadow-lovable: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-lovable-lg: 0 10px 40px rgba(0, 0, 0, 0.2);
  --shadow-glow: 0 0 20px rgba(124, 115, 255, 0.3);
  --shadow-glow-lg: 0 0 40px rgba(124, 115, 255, 0.4);

  /* Animation timing */
  --timing-fast: 0.2s;
  --timing-normal: 0.3s;
  --timing-slow: 0.6s;
  --easing: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .card-lovable {
      border-width: 2px;
      border-color: white;
    }

    .btn-lovable {
      border: 2px solid white;
    }

    .input-lovable {
      border: 2px solid white;
    }
  }

  /* Focus visible for better keyboard navigation */
  *:focus-visible {
    outline: 2px solid #7c73ff;
    outline-offset: 2px;
  }
}

/* Component styles */
@layer components {
  /* Lovable-style button */
  .btn-lovable {
    @apply relative overflow-hidden rounded-xl px-6 py-3 font-semibold text-white transition-all duration-300 ease-out;
    background: var(--gradient-lovable-light);
    box-shadow: var(--shadow-lovable);
    /* Cross-browser support */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform, box-shadow;
  }

  .btn-lovable:hover {
    @apply scale-105;
    box-shadow: var(--shadow-glow-lg);
    -webkit-transform: scale(1.05) translateZ(0);
    transform: scale(1.05) translateZ(0);
  }

  .btn-lovable:active {
    @apply scale-95;
    -webkit-transform: scale(0.95) translateZ(0);
    transform: scale(0.95) translateZ(0);
  }

  /* Lovable-style input */
  .input-lovable {
    @apply relative rounded-xl border-0 bg-white/10 backdrop-blur-sm px-4 py-3 text-white placeholder-white/60 transition-all duration-300;
    box-shadow: var(--shadow-lovable), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* Cross-browser support */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
  }

  .input-lovable:focus {
    @apply outline-none ring-2 ring-white/20;
    box-shadow: var(--shadow-glow), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Placeholder styling for cross-browser support */
  .input-lovable::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .input-lovable::-moz-placeholder {
    color: rgba(255, 255, 255, 0.6);
    opacity: 1;
  }

  .input-lovable:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  /* Lovable-style card */
  .card-lovable {
    @apply relative rounded-2xl backdrop-blur-sm border border-white/10 transition-all duration-300;
    background: var(--gradient-card);
    box-shadow: var(--shadow-lovable);
  }

  .card-lovable:hover {
    @apply scale-105 border-white/20;
    box-shadow: var(--shadow-lovable-lg);
  }

  /* Glass morphism effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }

  /* Animated gradient text */
  .text-gradient {
    @apply bg-clip-text text-transparent;
    background-image: var(--gradient-lovable-light);
  }
}

/* Enhanced animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(124, 115, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(124, 115, 255, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Utility animations */
@layer utilities {
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
  }
}
