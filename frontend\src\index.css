@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Simple, clean design system */
:root {
  /* Basic color palette */
  --primary-color: #3b82f6;
  --secondary-color: #6b7280;
  --background-color: #f9fafb;
  --text-color: #111827;
  --border-color: #e5e7eb;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: var(--background-color);
    color: var(--text-color);
  }

  /* Focus visible for better keyboard navigation */
  *:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

/* Simple component styles */
@layer components {
  /* Simple button */
  .btn-primary {
    @apply px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200;
  }

  /* Simple input */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
  }

  /* Simple card */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
}

/* Simple fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
}
