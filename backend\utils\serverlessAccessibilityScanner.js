const axios = require('axios');
const axeCore = require('axe-core');

class ServerlessAccessibilityScanner {
  constructor(options = {}) {
    this.options = {
      timeout: options.timeout || 30000, // 30 seconds for serverless
      userAgent: options.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      ...options
    };
  }

  /**
   * Initialize scanner (serverless-compatible)
   */
  async initialize() {
    console.log('🚀 Initializing serverless accessibility scanner...');
    return true;
  }

  /**
   * Fetch URL content (serverless-compatible)
   */
  async navigateToUrl(url) {
    try {
      console.log(`🌐 Fetching content from: ${url}`);

      const response = await axios.get(url, {
        timeout: this.options.timeout,
        headers: {
          'User-Agent': this.options.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        maxRedirects: 5,
        validateStatus: function (status) {
          return status >= 200 && status < 400;
        }
      });

      console.log('✅ Content fetched successfully');
      return {
        url: response.request.res?.responseUrl || url,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        title: this.extractTitle(response.data)
      };
    } catch (error) {
      console.error('❌ Failed to fetch URL:', error);
      throw new Error(`Fetch failed: ${error.message}`);
    }
  }

  /**
   * Extract title from HTML content
   */
  extractTitle(html) {
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1].trim() : 'Untitled';
  }

  /**
   * Perform accessibility analysis on HTML content
   */
  async runAccessibilityAnalysis(html, url, options = {}) {
    try {
      console.log('🔍 Running accessibility analysis...');

      // Create a simplified DOM-like structure for axe-core
      const results = await this.analyzeHtmlContent(html, url, options);

      console.log('✅ Accessibility analysis completed');
      return results;
    } catch (error) {
      console.error('❌ Accessibility analysis failed:', error);
      throw new Error(`Analysis failed: ${error.message}`);
    }
  }

  /**
   * Analyze HTML content for accessibility issues
   */
  async analyzeHtmlContent(html, url, options = {}) {
    // Basic HTML structure analysis
    const issues = [];
    const violations = [];
    const passes = [];
    
    // Check for common accessibility issues
    const checks = [
      this.checkImages(html),
      this.checkHeadings(html),
      this.checkLinks(html),
      this.checkForms(html),
      this.checkColors(html),
      this.checkLang(html),
      this.checkTitle(html)
    ];

    for (const check of checks) {
      const result = await check;
      if (result.violations.length > 0) {
        violations.push(...result.violations);
      }
      if (result.passes.length > 0) {
        passes.push(...result.passes);
      }
    }

    // Calculate scores
    const totalChecks = violations.length + passes.length;
    const score = totalChecks > 0 ? Math.round((passes.length / totalChecks) * 100) : 0;

    return {
      url,
      timestamp: new Date().toISOString(),
      violations,
      passes,
      incomplete: [],
      inapplicable: [],
      testEngine: {
        name: 'serverless-accessibility-scanner',
        version: '1.0.0'
      },
      testRunner: {
        name: 'serverless-accessibility-scanner'
      },
      testEnvironment: {
        userAgent: this.options.userAgent,
        windowWidth: 1280,
        windowHeight: 720
      },
      toolOptions: options,
      score,
      summary: {
        violations: violations.length,
        passes: passes.length,
        incomplete: 0,
        inapplicable: 0
      }
    };
  }

  /**
   * Check images for alt text
   */
  async checkImages(html) {
    const violations = [];
    const passes = [];
    
    const imgRegex = /<img[^>]*>/gi;
    const images = html.match(imgRegex) || [];
    
    images.forEach((img, index) => {
      const hasAlt = /alt\s*=\s*["'][^"']*["']/i.test(img);
      const isEmpty = /alt\s*=\s*["']\s*["']/i.test(img);
      
      if (!hasAlt) {
        violations.push({
          id: 'image-alt',
          impact: 'critical',
          description: 'Images must have alternate text',
          help: 'All img elements must have an alt attribute',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/image-alt',
          nodes: [{
            html: img,
            target: [`img:nth-child(${index + 1})`],
            failureSummary: 'Missing alt attribute'
          }]
        });
      } else if (isEmpty) {
        violations.push({
          id: 'image-alt',
          impact: 'minor',
          description: 'Images should have meaningful alternate text',
          help: 'Alt text should describe the image content',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/image-alt',
          nodes: [{
            html: img,
            target: [`img:nth-child(${index + 1})`],
            failureSummary: 'Empty alt attribute'
          }]
        });
      } else {
        passes.push({
          id: 'image-alt',
          description: 'Images have alternate text',
          nodes: [{
            html: img,
            target: [`img:nth-child(${index + 1})`]
          }]
        });
      }
    });
    
    return { violations, passes };
  }

  /**
   * Check heading structure
   */
  async checkHeadings(html) {
    const violations = [];
    const passes = [];
    
    const headingRegex = /<h([1-6])[^>]*>.*?<\/h[1-6]>/gi;
    const headings = [];
    let match;
    
    while ((match = headingRegex.exec(html)) !== null) {
      headings.push({
        level: parseInt(match[1]),
        html: match[0],
        text: match[0].replace(/<[^>]*>/g, '').trim()
      });
    }
    
    if (headings.length === 0) {
      violations.push({
        id: 'page-has-heading-one',
        impact: 'moderate',
        description: 'Page should have a heading structure',
        help: 'Page must contain a heading',
        helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/page-has-heading-one',
        nodes: [{
          html: '<html>',
          target: ['html'],
          failureSummary: 'No headings found on page'
        }]
      });
    } else {
      passes.push({
        id: 'page-has-heading-one',
        description: 'Page contains headings',
        nodes: headings.map((h, i) => ({
          html: h.html,
          target: [`h${h.level}:nth-child(${i + 1})`]
        }))
      });
    }
    
    return { violations, passes };
  }

  /**
   * Check links for accessible names
   */
  async checkLinks(html) {
    const violations = [];
    const passes = [];
    
    const linkRegex = /<a[^>]*>.*?<\/a>/gi;
    const links = html.match(linkRegex) || [];
    
    links.forEach((link, index) => {
      const text = link.replace(/<[^>]*>/g, '').trim();
      const hasHref = /href\s*=\s*["'][^"']*["']/i.test(link);
      
      if (hasHref && (!text || text.length < 2)) {
        violations.push({
          id: 'link-name',
          impact: 'serious',
          description: 'Links must have discernible text',
          help: 'Links must have an accessible name',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/link-name',
          nodes: [{
            html: link,
            target: [`a:nth-child(${index + 1})`],
            failureSummary: 'Link has no accessible name'
          }]
        });
      } else if (hasHref && text) {
        passes.push({
          id: 'link-name',
          description: 'Links have accessible names',
          nodes: [{
            html: link,
            target: [`a:nth-child(${index + 1})`]
          }]
        });
      }
    });
    
    return { violations, passes };
  }

  /**
   * Check forms for labels
   */
  async checkForms(html) {
    const violations = [];
    const passes = [];
    
    const inputRegex = /<input[^>]*>/gi;
    const inputs = html.match(inputRegex) || [];
    
    inputs.forEach((input, index) => {
      const hasLabel = /aria-label\s*=|aria-labelledby\s*=/i.test(input);
      const type = (input.match(/type\s*=\s*["']([^"']*)["']/i) || [])[1] || 'text';
      
      if (['text', 'email', 'password', 'tel', 'url', 'search'].includes(type) && !hasLabel) {
        violations.push({
          id: 'label',
          impact: 'critical',
          description: 'Form elements must have labels',
          help: 'Form elements should have an accessible name',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/label',
          nodes: [{
            html: input,
            target: [`input:nth-child(${index + 1})`],
            failureSummary: 'Form element has no label'
          }]
        });
      } else if (hasLabel) {
        passes.push({
          id: 'label',
          description: 'Form elements have labels',
          nodes: [{
            html: input,
            target: [`input:nth-child(${index + 1})`]
          }]
        });
      }
    });
    
    return { violations, passes };
  }

  /**
   * Check for color contrast (basic check)
   */
  async checkColors(html) {
    const violations = [];
    const passes = [];
    
    // Basic check for inline styles with colors
    const colorRegex = /style\s*=\s*["'][^"']*color\s*:\s*[^;"']*["']/gi;
    const colorElements = html.match(colorRegex) || [];
    
    if (colorElements.length > 0) {
      passes.push({
        id: 'color-contrast',
        description: 'Color usage detected (manual review recommended)',
        nodes: colorElements.map((elem, i) => ({
          html: elem,
          target: [`[style*="color"]:nth-child(${i + 1})`]
        }))
      });
    }
    
    return { violations, passes };
  }

  /**
   * Check for language attribute
   */
  async checkLang(html) {
    const violations = [];
    const passes = [];
    
    const hasLang = /<html[^>]*lang\s*=\s*["'][^"']*["']/i.test(html);
    
    if (!hasLang) {
      violations.push({
        id: 'html-has-lang',
        impact: 'serious',
        description: 'HTML element must have a lang attribute',
        help: 'The html element must have a lang attribute',
        helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/html-has-lang',
        nodes: [{
          html: '<html>',
          target: ['html'],
          failureSummary: 'Missing lang attribute'
        }]
      });
    } else {
      passes.push({
        id: 'html-has-lang',
        description: 'HTML element has lang attribute',
        nodes: [{
          html: '<html>',
          target: ['html']
        }]
      });
    }
    
    return { violations, passes };
  }

  /**
   * Check for page title
   */
  async checkTitle(html) {
    const violations = [];
    const passes = [];
    
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    
    if (!titleMatch || !titleMatch[1].trim()) {
      violations.push({
        id: 'document-title',
        impact: 'serious',
        description: 'Documents must have a title',
        help: 'Documents must have a title to aid in navigation',
        helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/document-title',
        nodes: [{
          html: '<title>',
          target: ['title'],
          failureSummary: 'Missing or empty title'
        }]
      });
    } else {
      passes.push({
        id: 'document-title',
        description: 'Document has a title',
        nodes: [{
          html: titleMatch[0],
          target: ['title']
        }]
      });
    }
    
    return { violations, passes };
  }

  /**
   * Main scan method
   */
  async scan(url, options = {}) {
    try {
      await this.initialize();
      
      const pageInfo = await this.navigateToUrl(url);
      const analysisResults = await this.runAccessibilityAnalysis(pageInfo.data, url, options);
      
      return {
        pageInfo: {
          url: pageInfo.url,
          title: pageInfo.title,
          status: pageInfo.status,
          statusText: pageInfo.statusText
        },
        accessibility: analysisResults,
        metadata: {
          scanDate: new Date().toISOString(),
          scanner: 'serverless-accessibility-scanner',
          version: '1.0.0'
        }
      };
    } catch (error) {
      console.error('❌ Scan failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup (no-op for serverless)
   */
  async cleanup() {
    console.log('🧹 Cleanup completed (serverless)');
  }
}

module.exports = ServerlessAccessibilityScanner;
