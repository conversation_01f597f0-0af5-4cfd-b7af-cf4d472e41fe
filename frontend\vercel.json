{"env": {"REACT_APP_API_BASE_URL": "https://accessibility-analyzer-jw9b8tkry-jan<PERSON><PERSON>-tiwaris-projects.vercel.app", "REACT_APP_API_TIMEOUT": "30000", "REACT_APP_FIREBASE_API_KEY": "AIzaSyDnOUeIiUCCTd4jCBjMFnzV57MiXSs-l9E", "REACT_APP_FIREBASE_AUTH_DOMAIN": "accessibility-analyzer-cc6c6.firebaseapp.com", "REACT_APP_FIREBASE_PROJECT_ID": "accessibility-analyzer-cc6c6", "REACT_APP_FIREBASE_STORAGE_BUCKET": "accessibility-analyzer-cc6c6.firebasestorage.app", "REACT_APP_FIREBASE_MESSAGING_SENDER_ID": "792194922829", "REACT_APP_FIREBASE_APP_ID": "1:792194922829:web:62b78a1b52aff3341b9f04", "REACT_APP_NAME": "Accessibility Analyzer", "REACT_APP_VERSION": "1.0.0", "REACT_APP_ENVIRONMENT": "production"}, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}