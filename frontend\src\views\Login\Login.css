/* Login page animations and styles */

.login-container {
  animation: fadeIn 0.6s ease-out;
}

.login-left-panel {
  animation: slideInLeft 0.8s ease-out;
}

.login-right-panel {
  animation: slideInRight 0.8s ease-out;
}

.login-form {
  animation: slideUp 0.6s ease-out 0.2s both;
}

.gradient-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #ffd3a5 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

.floating-elements::before {
  content: '';
  position: absolute;
  top: 10%;
  left: 10%;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-elements::after {
  content: '';
  position: absolute;
  bottom: 20%;
  right: 15%;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Button hover effects */
.login-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Input focus effects */
.login-input {
  transition: all 0.3s ease;
}

.login-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

/* Mobile responsiveness */
@media (max-width: 1024px) {
  .login-left-panel {
    animation: slideUp 0.6s ease-out;
  }
  
  .login-right-panel {
    animation: slideUp 0.6s ease-out 0.2s both;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .login-container,
  .login-left-panel,
  .login-right-panel,
  .login-form,
  .login-button,
  .login-input {
    animation: none;
    transition: none;
  }
  
  .gradient-background {
    animation: none;
  }
  
  .floating-elements::before,
  .floating-elements::after {
    animation: none;
  }
}
