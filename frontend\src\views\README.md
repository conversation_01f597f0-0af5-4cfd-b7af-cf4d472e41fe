# Views Directory

This directory contains main application screens and page components.

## Structure

- `Home/` - Landing page and main dashboard
- `Analysis/` - Analysis creation and results pages
- `Dashboard/` - User dashboard and analytics
- `Auth/` - Authentication pages (login, register, etc.)
- `Profile/` - User profile and settings

## View Guidelines

- Each view should be in its own directory
- Include route configuration
- Implement proper loading states
- Handle error boundaries
- Use React Router for navigation
