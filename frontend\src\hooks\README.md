# Hooks Directory

This directory contains custom React hooks for the application.

## Structure

- `useAuth.js` - Authentication state management
- `useApi.js` - API request management
- `useLocalStorage.js` - Local storage management
- `useAnalysis.js` - Analysis state management

## Hook Guidelines

- Follow React hooks naming convention (use prefix)
- Implement proper cleanup in useEffect
- Use PropTypes for better type safety
- Include JSDoc comments
- Test hooks thoroughly
