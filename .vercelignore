# Dependencies
node_modules/
frontend/node_modules/
backend/node_modules/

# Build outputs
frontend/build/
backend/dist/

# Environment files (local only)
.env.local
.env.development.local
.env.test.local

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
**/__tests__/
**/*.test.js
**/*.spec.js

# Documentation
README.md
docs/
