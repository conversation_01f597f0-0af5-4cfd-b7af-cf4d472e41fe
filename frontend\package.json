{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "firebase": "^11.9.1", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=3001 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:watch": "react-scripts test --watchAll=false", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "react-scripts test --coverage --watchAll=false --ci", "test:integration": "react-scripts test --testPathPattern=integration", "test:unit": "react-scripts test --testPathPattern=__tests__ --testPathIgnorePatterns=integration", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}