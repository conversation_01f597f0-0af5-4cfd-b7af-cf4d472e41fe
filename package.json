{"name": "accessibility-analyzer", "version": "1.0.0", "description": "A comprehensive web application for analyzing and improving website accessibility compliance with WCAG guidelines.", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "build": "cd frontend && npm install && npm run build", "setup:firebase": "node backend/utils/firebase-setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "axe-core": "^4.10.3", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "morgan": "^1.10.0", "playwright": "^1.53.1", "react-router-dom": "^7.6.2"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.1.10", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^4.1.10"}}